import { http } from '@/http/request/alova'

/**
 * 证件照数据项
 */
export interface IPhotoItem {
  id: number
  name: string
  widthPx: number
  heightPx: number
  widthMm: number
  heightMm: number
  icon: number
  sort: number
  category: number
  dpi: number
}

/**
 * 分页请求参数
 */
export interface IPhotoListParams {
  pageNum: number
  pageSize: number
  type: number // 分类类型：1-常用尺寸，2-各类证件，3-各类签证
}

/**
 * 搜索请求参数
 */
export interface IPhotoSearchParams {
  pageNum: number
  pageSize: number
  type: number // 固定为0，表示搜索
  name: string // 搜索关键词
}

/**
 * 分页响应数据
 */
export interface IPhotoListResponse {
  records: IPhotoItem[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 获取美颜效果状态
 * @returns Promise<number> 返回美颜效果状态值
 */
export function getWebGlow() {
  return http.Post<number>('/api/getWebGlow', {
    data: {}, // POST 请求需要 data 字段，即使是空对象
    meta: {
      ignoreAuth: true, // 忽略认证，不需要登录即可访问
    },
  })
}

/**
 * 获取证件照列表
 * @param params 分页参数
 * @returns Promise<IPhotoListResponse>
 */
export function getPhotoList(params: IPhotoListParams) {
  return http.Get<IPhotoListResponse>('/item/itemList', {
    params,
    meta: {
      ignoreAuth: true, // 忽略认证，不需要登录即可访问
    },
  })
}

/**
 * 搜索证件照
 * @param params 搜索参数
 * @returns Promise<IPhotoListResponse>
 */
export function searchPhotoList(params: IPhotoSearchParams) {
  return http.Get<IPhotoListResponse>('/item/itemList', {
    params,
    meta: {
      ignoreAuth: true, // 忽略认证，不需要登录即可访问
    },
  })
}
